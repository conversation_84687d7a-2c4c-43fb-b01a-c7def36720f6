import { Period as PeriodLink } from "../cf-link/CfLinkActivities";
import moment from "moment";

export type PeriodType = "standard-period" | "1-week" | "calendar-months" | "quarterly";

export interface Period {
	periodType: PeriodType;
	periodSince: Date | number;
}

type DefaultPeriodType = {
	key: PeriodType;
	displayValue: string;
};

export const StandardPeriodLengthDays = 28;

export const isDatePeriod = (periodType: PeriodType) => periodType === "standard-period" || periodType === "1-week";

export function defaultPeriodsDynamic(periodLength: number): DefaultPeriodType[] {
	const standardPeriodLengthString = periodLengthString(periodLength);
	const standardPeriodDisplayValue = `6 x ${standardPeriodLengthString}`;
	return [
		{ key: "standard-period", displayValue: standardPeriodDisplayValue },
		{ key: "1-week", displayValue: "6 x 1-week" },
		{ key: "calendar-months", displayValue: "6 calendar months" },
		{ key: "quarterly", displayValue: "Quarterly" },
	];
}

export function periodLengthString(periodLength: number) {
	return periodLength !== 0 && periodLength % 7 === 0 ? `${periodLength / 7}-week` : `${periodLength}-day`;
}

export function formatDateToISODate(date: Date): string {
	return date.toISOString().split("T")[0]; // Format to YYYY-MM-DD
}

export function getDatePeriods(period: Period, customServingGroupLength?: number): PeriodLink[] {
	//TODO - check if customServingGroupLength is always added!

	function generatePeriods(periodSince: Date, periodLength: number, numberOfPeriods: number) {
		const periodLinks: PeriodLink[] = [];
		for (let i = 0; i < numberOfPeriods; i++) {
			const to = new Date(periodSince.getFullYear(), periodSince.getMonth(), periodSince.getDate() - i * periodLength);
			const from = new Date(periodSince.getFullYear(), periodSince.getMonth(), periodSince.getDate() - ((i + 1) * periodLength - 1));

			periodLinks.push({
				from: formatDateToISODate(from),
				to: formatDateToISODate(to),
			});
		}
		return periodLinks;
	}

	function generateMonthsPeriods(firstMonth: number, monthNumbers: number, numberOfPeriods: number) {
		const periodLinks: PeriodLink[] = [];

		for (let i = 0; i < numberOfPeriods; i++) {
			const to = moment()
				.month(firstMonth)
				.subtract(i * monthNumbers, "month")
				.endOf("month");
			const from = moment(to)
				.subtract(monthNumbers - 1, "month")
				.startOf("month");

			periodLinks.push({
				from: from.toISOString(true).split("T")[0],
				to: to.toISOString(true).split("T")[0],
			});
		}
		return periodLinks;
	}

	// periodSince is not correctly typed as Date. We need to manually convert it.
	if (typeof period.periodSince === "string") {
		const periodSince = new Date(period.periodSince);
		const servingLength = customServingGroupLength ?? 7;
		const normalizedStandardLength = Math.max(1, Math.floor(28 / servingLength)) * servingLength;

		const previousMonth = moment(periodSince).subtract(1, "month").month();

		const result = [
			...generatePeriods(periodSince, normalizedStandardLength, 4),
			...generatePeriods(periodSince, servingLength, 4),
			...generateMonthsPeriods(previousMonth, 3, 5),
		];
		console.log(result);
		// throw new Error("Not implemented");
		return result;
		// return generatePeriods(periodSince, normalizedStandardLength, 6);
	} else if (typeof period.periodSince === "number") {
		if (period.periodType === "calendar-months") {
			return generateMonthsPeriods(period.periodSince, 1, 6);
		} else if (period.periodType === "quarterly") {
			return generateMonthsPeriods(period.periodSince, 3, 6);
		}
	}
	throw new Error(`Unsupported period type: ${JSON.stringify(period)}`);
}
