import { Period } from "../EfficiencyReportData";
import moment from "moment";

export type PeriodNaming = {
	code: string;
	from: string;
	to: string;
	label: string;
	labelDesc: string | undefined;
	textName: string;
};

/**
 * Get month names for a given language code using moment.js
 */
function getMonthNames(langCode: string, format: "long" | "short" = "long"): string[] {
	// Set moment locale
	const originalLocale = moment.locale();
	moment.locale(langCode);

	const monthList = [...Array(12).keys()]; // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]

	const getMonthName = (monthIndex: number) => {
		const monthMoment = moment().month(monthIndex);
		return format === "short" ? monthMoment.format("MMM") : monthMoment.format("MMMM");
	};

	const result = monthList.map(getMonthName);

	// Restore original locale
	moment.locale(originalLocale);

	return result;
}

/**
 * Format a date using the given language code and custom format (short format for graphItemLabel)
 */
function formatDateByLanguage(dateStr: string, langCode: string, dateFormat: string, showYear: boolean = true): string {
	try {
		const dateMoment = moment(dateStr);

		if (!dateMoment.isValid()) {
			return dateStr;
		}

		if (dateFormat === "t") {
			// Use moment to format dates with thousand-days calendar format
			const localizedMoment = dateMoment.clone().locale(langCode);
			return localizedMoment.format("t");
		} else {
			// Use Intl.DateTimeFormat for short format
			const options: Intl.DateTimeFormatOptions = {
				month: "short",
				day: "numeric",
			};

			if (showYear) {
				options.year = "numeric";
			}

			const formatter = new Intl.DateTimeFormat(langCode, options);
			return formatter.format(dateMoment.toDate());
		}
	} catch (error) {
		return dateStr;
	}
}

/**
 * Format a date using the given language code and custom format (long format for textName)
 */
function formatDateByLanguageLong(dateStr: string, langCode: string, dateFormat: string, showYear: boolean = true): string {
	try {
		const dateMoment = moment(dateStr);

		if (!dateMoment.isValid()) {
			return dateStr;
		}

		if (dateFormat === "t") {
			// Use moment to format dates with thousand-days calendar format
			const localizedMoment = dateMoment.clone().locale(langCode);
			return localizedMoment.format("t");
		} else {
			// Use Intl.DateTimeFormat for long format
			const options: Intl.DateTimeFormatOptions = {
				month: "long",
				day: "numeric",
			};

			if (showYear) {
				options.year = "numeric";
			}

			const formatter = new Intl.DateTimeFormat(langCode, options);
			return formatter.format(dateMoment.toDate());
		}
	} catch (error) {
		return dateStr;
	}
}

/**
 * Format a date range with smart year handling - if both dates need years and are in the same year,
 * only show the year once at the end
 */
function formatDateRangeByLanguage(
	fromDateStr: string,
	toDateStr: string,
	langCode: string,
	dateFormat: string,
	showYear: boolean = true,
): string {
	try {
		const fromMoment = moment(fromDateStr);
		const toMoment = moment(toDateStr);

		if (!fromMoment.isValid() || !toMoment.isValid()) {
			return `${fromDateStr}–${toDateStr}`;
		}

		if (dateFormat === "t") {
			// For thousand-days calendar, format each date separately
			const fromFormatted = formatDateByLanguage(fromDateStr, langCode, dateFormat, showYear);
			const toFormatted = formatDateByLanguage(toDateStr, langCode, dateFormat, showYear);
			return `${fromFormatted}–${toFormatted}`;
		} else {
			// Check if both dates are in the same year and we need to show years
			const sameYear = fromMoment.year() === toMoment.year();

			if (showYear && sameYear) {
				// Format dates without year, then add year once at the end
				const fromFormatted = formatDateByLanguage(fromDateStr, langCode, dateFormat, false);
				const toFormatted = formatDateByLanguage(toDateStr, langCode, dateFormat, false);
				const year = fromMoment.year();
				return `${fromFormatted}–${toFormatted}, ${year}`;
			} else {
				// Format each date separately (either no year needed, or different years)
				const fromFormatted = formatDateByLanguage(fromDateStr, langCode, dateFormat, showYear);
				const toFormatted = formatDateByLanguage(toDateStr, langCode, dateFormat, showYear);
				return `${fromFormatted}–${toFormatted}`;
			}
		}
	} catch (error) {
		return `${fromDateStr}–${toDateStr}`;
	}
}

/**
 * Calculate the number of days between two ISO date strings using moment.js
 */
function calculateDaysBetween(from: string, to: string): number {
	const fromMoment = moment(from);
	const toMoment = moment(to);
	return toMoment.diff(fromMoment, "days") + 1; // +1 to include both start and end dates
}

/**
 * Check if a period spans exactly one calendar month using moment.js
 */
function isCalendarMonth(from: string, to: string): boolean {
	const fromMoment = moment(from);
	const toMoment = moment(to);

	// Check if from is the first day of the month and to is the last day of the same month
	const startOfMonth = fromMoment.clone().startOf("month");
	const endOfMonth = fromMoment.clone().endOf("month");

	return fromMoment.isSame(startOfMonth, "day") && toMoment.isSame(endOfMonth, "day") && fromMoment.isSame(toMoment, "month");
}

/**
 * Check if a period spans a range of calendar months using moment.js
 */
function isCalendarMonthRange(from: string, to: string): boolean {
	const fromMoment = moment(from);
	const toMoment = moment(to);

	// Check if from is the first day of a month
	const startOfFromMonth = fromMoment.clone().startOf("month");
	if (!fromMoment.isSame(startOfFromMonth, "day")) {
		return false;
	}

	// Check if to is the last day of a month
	const endOfToMonth = toMoment.clone().endOf("month");
	if (!toMoment.isSame(endOfToMonth, "day")) {
		return false;
	}

	// Check if it spans multiple months
	return !fromMoment.isSame(toMoment, "month");
}

/**
 * Determine the type of a single period
 */
function getPeriodType(period: Period): "calendar-month" | "calendar-month-range" | "same-length" {
	if (isCalendarMonth(period.from, period.to)) {
		return "calendar-month";
	} else if (isCalendarMonthRange(period.from, period.to)) {
		return "calendar-month-range";
	} else {
		return "same-length";
	}
}

/**
 * Group periods by their type and length (for same-length periods)
 */
function groupPeriodsByType(periods: Period[]): Map<string, Period[]> {
	const groups = new Map<string, Period[]>();

	for (const period of periods) {
		const type = getPeriodType(period);
		let key: string;

		if (type === "same-length") {
			const length = calculateDaysBetween(period.from, period.to);
			key = `${type}-${length}`;
		} else {
			key = type;
		}

		if (!groups.has(key)) {
			groups.set(key, []);
		}
		groups.get(key)!.push(period);
	}

	return groups;
}

/**
 * Process calendar month periods (Type A)
 */
function processCalendarMonthPeriods(periods: Period[], langCode: string): PeriodNaming[] {
	const shortMonthNames = getMonthNames(langCode, "short");
	const longMonthNames = getMonthNames(langCode, "long");

	return periods.map((period) => {
		const fromMoment = moment(period.from);
		const shortMonthName = shortMonthNames[fromMoment.month()];
		const longMonthName = longMonthNames[fromMoment.month()];

		return {
			code: `${period.from}–${period.to}`,
			from: period.from,
			to: period.to,
			label: shortMonthName,
			labelDesc: undefined,
			textName: longMonthName,
		};
	});
}

/**
 * Process calendar month range periods (Type B)
 */
function processCalendarMonthRangePeriods(periods: Period[], langCode: string): PeriodNaming[] {
	const shortMonthNames = getMonthNames(langCode, "short");
	const longMonthNames = getMonthNames(langCode, "long");

	return periods.map((period) => {
		const fromMoment = moment(period.from);
		const toMoment = moment(period.to);

		const firstShortMonth = shortMonthNames[fromMoment.month()];
		const lastShortMonth = shortMonthNames[toMoment.month()];
		const shortLabel = `${firstShortMonth}–${lastShortMonth}`;

		const firstLongMonth = longMonthNames[fromMoment.month()];
		const lastLongMonth = longMonthNames[toMoment.month()];
		const longLabel = `${firstLongMonth}–${lastLongMonth}`;

		return {
			code: `${period.from}–${period.to}`,
			from: period.from,
			to: period.to,
			label: shortLabel,
			labelDesc: undefined,
			textName: longLabel,
		};
	});
}

/**
 * Process same length periods (Type C)
 */
function processSameLengthPeriods(
	periods: Period[],
	langCode: string,
	dateFormat: string,
	overallLatestMoment: moment.Moment
): PeriodNaming[] {
	const periodLength = calculateDaysBetween(periods[0].from, periods[0].to);

	// Determine period description (plural form for graphDescLabel, singular for textName)
	const periodDescSingular = periodLength % 7 === 0 ? `${periodLength / 7}-week` : `${periodLength}-day`;
	const periodDescPlural = periodLength % 7 === 0 ? `${periodLength / 7}-week` : `${periodLength}-day`;
	const periodDescWithLabelPlural = `${periodDescPlural} periods`;
	const periodDescWithLabelSingular = `${periodDescSingular} period`;

	// Find the latest period within this group
	const groupLatestMoment = moment.max(periods.map((p) => moment(p.to)));

	return periods.map((period) => {
		const periodMoment = moment(period.to);
		const monthsDiff = overallLatestMoment.diff(periodMoment, "months");
		const showYear = monthsDiff >= 11;

		const endDateLong = formatDateByLanguageLong(period.to, langCode, dateFormat, showYear);

		// Check if this period is the latest within its group AND the group's latest is the overall latest
		const isLatestInGroup = periodMoment.isSame(groupLatestMoment, "day");
		const isGroupLatestOverall = groupLatestMoment.isSame(overallLatestMoment, "day");
		const isLatestPeriod = isLatestInGroup && isGroupLatestOverall;

		const textName = isLatestPeriod ? `latest ${periodDescWithLabelSingular}` : `${periodDescWithLabelSingular} ending ${endDateLong}`;

		// For same length periods (Type C), use from–to format for label with smart year handling
		const label = formatDateRangeByLanguage(period.from, period.to, langCode, dateFormat, showYear);

		return {
			code: `${period.from}–${period.to}`,
			from: period.from,
			to: period.to,
			label: label,
			labelDesc: periodDescWithLabelPlural,
			textName: textName,
		};
	});
}

/**
 * Determine the type of periods and generate appropriate naming
 * Handles mixed period types by processing each type separately
 */
export function getPeriodNamings(periods: Period[], langCode: string, dateFormat: string): PeriodNaming[] {
	if (periods.length === 0) {
		return [];
	}

	// Find the overall latest period across all types
	const overallLatestMoment = moment.max(periods.map((p) => moment(p.to)));

	// Group periods by type and length
	const periodGroups = groupPeriodsByType(periods);

	// Process each group and collect results
	const allResults: PeriodNaming[] = [];

	for (const [groupKey, groupPeriods] of periodGroups) {
		let groupResults: PeriodNaming[];

		if (groupKey === "calendar-month") {
			groupResults = processCalendarMonthPeriods(groupPeriods, langCode);
		} else if (groupKey === "calendar-month-range") {
			groupResults = processCalendarMonthRangePeriods(groupPeriods, langCode);
		} else {
			// same-length periods
			groupResults = processSameLengthPeriods(groupPeriods, langCode, dateFormat, overallLatestMoment);
		}

		allResults.push(...groupResults);
	}

	// Sort results to maintain original order
	const resultMap = new Map<string, PeriodNaming>();
	for (const result of allResults) {
		resultMap.set(result.code, result);
	}

	return periods.map(period => {
		const code = `${period.from}–${period.to}`;
		return resultMap.get(code)!;
	});
}
