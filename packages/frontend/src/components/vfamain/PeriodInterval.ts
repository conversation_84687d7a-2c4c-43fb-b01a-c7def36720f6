import { PeriodType } from 'pigbot-core/src/vfamain/Periods';

export type PeriodInterval = {
	from: moment.Moment;
	to: moment.Moment;
};

export function getPeriodInterval(periodType: PeriodType, periodUntil: moment.Moment, periodLength: number): PeriodInterval {
	const from = resolveTo(periodType, periodUntil, periodLength);
	const to = periodUntil;

	return { from, to };
}

function resolveTo(periodType: PeriodType, periodUntil: moment.Moment, periodLength: number): moment.Moment {
	switch (periodType) {
		case 'standard-period':
			return periodUntil.clone().subtract(6 * periodLength, 'days');
		case '1-week':
			return periodUntil.clone().subtract(6 * 7, 'days');
		case 'calendar-months':
			return periodUntil.clone().subtract(6 * 1, 'months');
		case 'quarterly':
			return periodUntil.clone().subtract(4 * 1, 'quarters');
	}
}
